"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFocusableDate = getFocusableDate;
/**
 * Calculates the next date that should be focused in the calendar.
 *
 * This function determines the next focusable date based on the movement
 * direction, constraints, and calendar configuration.
 *
 * @param moveBy The unit of movement (e.g., "day", "week").
 * @param moveDir The direction of movement ("before" or "after").
 * @param refDate The reference date from which to calculate the next focusable
 *   date.
 * @param navStart The earliest date the user can navigate to.
 * @param navEnd The latest date the user can navigate to.
 * @param props The DayPicker props, including calendar configuration options.
 * @param dateLib The date library to use for date manipulation.
 * @returns The next focusable date.
 */
function getFocusableDate(moveBy, moveDir, refDate, navStart, navEnd, props, dateLib) {
    const { ISOWeek, broadcastCalendar } = props;
    const { addDays, addMonths, addWeeks, addYears, endOfBroadcastWeek, endOfISOWeek, endOfWeek, max, min, startOfBroadcastWeek, startOfISOWeek, startOfWeek, } = dateLib;
    const moveFns = {
        day: addDays,
        week: addWeeks,
        month: addMonths,
        year: addYears,
        startOfWeek: (date) => broadcastCalendar
            ? startOfBroadcastWeek(date, dateLib)
            : ISOWeek
                ? startOfISOWeek(date)
                : startOfWeek(date),
        endOfWeek: (date) => broadcastCalendar
            ? endOfBroadcastWeek(date)
            : ISOWeek
                ? endOfISOWeek(date)
                : endOfWeek(date),
    };
    let focusableDate = moveFns[moveBy](refDate, moveDir === "after" ? 1 : -1);
    if (moveDir === "before" && navStart) {
        focusableDate = max([navStart, focusableDate]);
    }
    else if (moveDir === "after" && navEnd) {
        focusableDate = min([navEnd, focusableDate]);
    }
    return focusableDate;
}
