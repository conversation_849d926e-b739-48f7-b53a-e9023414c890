import { useState, useEffect, useRef, useMemo } from 'react'
import { <PERSON><PERSON>, use<PERSON>rame, useLoader, useThree } from '@react-three/fiber'
import { OrbitControls, Text, Float, Environment, ContactShadows, PerspectiveCamera, useTexture } from '@react-three/drei'
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react'
import * as THREE from 'three'

// Hook to detect mobile devices
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return isMobile
}

// 3D Image Plane Component
function ImagePlane({ texture, position, rotation, scale, opacity = 1, isActive, isMobile }) {
  const meshRef = useRef()

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.material.opacity = opacity
      // Only add subtle rotation to non-active images
      if (!isActive) {
        meshRef.current.rotation.y = rotation + Math.sin(state.clock.elapsedTime * 0.3) * 0.01
      } else {
        meshRef.current.rotation.y = rotation
      }
    }
  })

  // Adjust plane size based on device
  const planeSize = isMobile ? [2, 1.4] : [2.5, 1.8]

  return (
    <mesh ref={meshRef} position={position} scale={scale}>
      <planeGeometry args={planeSize} />
      <meshStandardMaterial
        map={texture}
        transparent
        opacity={opacity}
        side={THREE.DoubleSide}
      />
    </mesh>
  )
}

// 3D Carousel Scene Component
function CarouselScene({ currentIndex, images, isMobile }) {
  const groupRef = useRef()
  const textures = useTexture(images.map(img => img.src))

  useFrame((state) => {
    if (groupRef.current) {
      // Gentle floating animation for the whole group
      groupRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.6) * 0.05
    }
  })

  const getImagePosition = (index, currentIndex, totalImages, isMobile) => {
    const offset = index - currentIndex

    // Adjust spacing based on device
    const sideDistance = isMobile ? 2.2 : 3
    const hiddenDistance = isMobile ? 4 : 6
    const depth = isMobile ? -0.5 : -1
    const hiddenDepth = isMobile ? -2 : -3

    if (offset === 0) {
      // Center position - active image
      return [0, 0, 0]
    } else if (offset === 1 || (offset === -(totalImages - 1))) {
      // Right position - next image
      return [sideDistance, 0, depth]
    } else if (offset === -1 || (offset === (totalImages - 1))) {
      // Left position - previous image
      return [-sideDistance, 0, depth]
    } else {
      // Hidden positions for other images
      const side = offset > 0 ? 1 : -1
      return [side * hiddenDistance, 0, hiddenDepth]
    }
  }

  const getImageScale = (index, currentIndex, totalImages, isMobile) => {
    const offset = index - currentIndex

    if (offset === 0) {
      // Center image - largest (smaller on mobile)
      return isMobile ? [1.2, 1.2, 1.2] : [1.4, 1.4, 1.4]
    } else if (Math.abs(offset) === 1 || Math.abs(offset) === (totalImages - 1)) {
      // Side images - medium (smaller on mobile)
      return isMobile ? [0.8, 0.8, 0.8] : [1, 1, 1]
    } else {
      // Hidden images - smallest
      return isMobile ? [0.5, 0.5, 0.5] : [0.7, 0.7, 0.7]
    }
  }

  const getImageOpacity = (index, currentIndex, totalImages) => {
    const offset = index - currentIndex

    if (offset === 0) {
      // Center image - fully visible
      return 1
    } else if (Math.abs(offset) === 1 || Math.abs(offset) === (totalImages - 1)) {
      // Side images - semi-transparent
      return 0.7
    } else {
      // Hidden images - very transparent
      return 0.3
    }
  }

  return (
    <group ref={groupRef}>
      {images.map((image, index) => {
        const isActive = index === currentIndex
        const position = getImagePosition(index, currentIndex, images.length, isMobile)
        const scale = getImageScale(index, currentIndex, images.length, isMobile)
        const opacity = getImageOpacity(index, currentIndex, images.length)

        return (
          <Float
            key={index}
            speed={isActive ? 1.5 : 0.8}
            rotationIntensity={isActive ? (isMobile ? 0.05 : 0.1) : 0.05}
            floatIntensity={isActive ? (isMobile ? 0.2 : 0.3) : 0.1}
          >
            <ImagePlane
              texture={textures[index]}
              position={position}
              rotation={0}
              scale={scale}
              opacity={opacity}
              isActive={isActive}
              isMobile={isMobile}
            />
          </Float>
        )
      })}
      
      {/* Center glow effect */}
      <pointLight position={[0, 0, 0]} intensity={0.5} color="#60a5fa" />
      
      {/* Floating particles - Reduced count on mobile */}
      {Array.from({ length: isMobile ? 15 : 30 }).map((_, i) => (
        <Float
          key={i}
          speed={1 + Math.random() * (isMobile ? 1 : 2)}
          rotationIntensity={isMobile ? 0.3 : 0.5}
          floatIntensity={isMobile ? 0.5 : 0.8}
        >
          <mesh position={[
            (Math.random() - 0.5) * (isMobile ? 6 : 10),
            (Math.random() - 0.5) * (isMobile ? 4 : 6),
            (Math.random() - 0.5) * (isMobile ? 6 : 10)
          ]}>
            <sphereGeometry args={[0.01 + Math.random() * (isMobile ? 0.02 : 0.03)]} />
            <meshStandardMaterial
              color={i % 3 === 0 ? "#60a5fa" : i % 3 === 1 ? "#f59e0b" : "#10b981"}
              emissive={i % 3 === 0 ? "#1e40af" : i % 3 === 1 ? "#d97706" : "#047857"}
              emissiveIntensity={0.4 + Math.random() * 0.3}
              transparent
              opacity={isMobile ? 0.5 : 0.7}
            />
          </mesh>
        </Float>
      ))}

      {/* Additional light rays */}
      <spotLight
        position={[5, 5, 5]}
        intensity={0.3}
        angle={0.2}
        penumbra={1}
        color="#60a5fa"
        castShadow
      />
      <spotLight
        position={[-5, 5, -5]}
        intensity={0.3}
        angle={0.2}
        penumbra={1}
        color="#f59e0b"
        castShadow
      />
    </group>
  )
}

// Main 3D Image Carousel Component
const ThreeDImageCarousel = () => {
  const images = [
    {
      src: '/b1.jpg',
      alt: 'Construction Project 1',
      title: 'Industrial Construction',
      description: 'Advanced industrial facility construction'
    },
    {
      src: '/b2.jpg',
      alt: 'Construction Project 2',
      title: 'Infrastructure Development',
      description: 'Modern infrastructure solutions'
    },
    {
      src: '/b3.jpg',
      alt: 'Construction Project 3',
      title: 'Engineering Excellence',
      description: 'Precision engineering projects'
    }
  ]

  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isHovered, setIsHovered] = useState(false)
  const isMobile = useIsMobile()

  // Touch gesture handling
  const [touchStart, setTouchStart] = useState(null)
  const [touchEnd, setTouchEnd] = useState(null)

  // Auto-scroll functionality
  useEffect(() => {
    if (!isAutoPlaying || isHovered) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === images.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, isHovered, images.length])

  const goToSlide = (index) => {
    setCurrentIndex(index)
  }

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? images.length - 1 : currentIndex - 1)
  }

  const goToNext = () => {
    setCurrentIndex(currentIndex === images.length - 1 ? 0 : currentIndex + 1)
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
  }

  // Touch gesture handlers
  const handleTouchStart = (e) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrevious()
    }
  }

  return (
    <div
      className="relative w-full h-full rounded-2xl overflow-hidden group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        background: `
          linear-gradient(135deg, #667eea 0%, #764ba2 100%),
          radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%),
          radial-gradient(circle at 70% 30%, rgba(255,255,255,0.05) 0%, transparent 50%)
        `,
        boxShadow: `
          0 25px 50px -12px rgba(0, 0, 0, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 0 100px rgba(59, 130, 246, 0.3)
        `,
        transform: 'perspective(1200px) rotateX(3deg) rotateY(-2deg) translateZ(20px)',
        transformStyle: 'preserve-3d',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)'
      }}
    >
      {/* 3D Canvas */}
      <Canvas
        camera={{
          position: isMobile ? [0, 0.5, 4] : [0, 1, 5],
          fov: isMobile ? 70 : 60
        }}
        style={{
          background: 'transparent',
          filter: 'drop-shadow(0 20px 40px rgba(0,0,0,0.3))'
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: isMobile ? "default" : "high-performance",
          pixelRatio: isMobile ? Math.min(window.devicePixelRatio, 2) : window.devicePixelRatio
        }}
      >
        {/* Enhanced Lighting - Optimized for mobile */}
        <ambientLight intensity={isMobile ? 0.4 : 0.3} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={isMobile ? 0.8 : 1}
          castShadow={!isMobile}
          shadow-mapSize-width={isMobile ? 1024 : 2048}
          shadow-mapSize-height={isMobile ? 1024 : 2048}
        />
        <pointLight position={[-10, -10, -10]} intensity={isMobile ? 0.2 : 0.3} color="#60a5fa" />
        {!isMobile && (
          <spotLight
            position={[0, 10, 0]}
            intensity={0.5}
            angle={0.3}
            penumbra={1}
            color="#ffffff"
          />
        )}
        
        {/* Environment for reflections - Disabled on mobile for performance */}
        {!isMobile && <Environment preset="city" />}

        {/* 3D Carousel */}
        <CarouselScene currentIndex={currentIndex} images={images} isMobile={isMobile} />

        {/* Contact Shadows - Simplified on mobile */}
        <ContactShadows
          position={[0, -2, 0]}
          opacity={isMobile ? 0.2 : 0.4}
          scale={isMobile ? 6 : 10}
          blur={isMobile ? 1 : 2}
          far={isMobile ? 2 : 4}
        />
        
        {/* Camera Controls - Adjusted for mobile */}
        <OrbitControls
          enablePan={false}
          enableZoom={false}
          enableRotate={!isMobile} // Disable rotation on mobile for better performance
          autoRotate={!isHovered && !isMobile} // Disable auto-rotate on mobile
          autoRotateSpeed={0.3}
          minPolarAngle={Math.PI / 2.5}
          maxPolarAngle={Math.PI / 1.8}
          minAzimuthAngle={isMobile ? 0 : -Math.PI / 6}
          maxAzimuthAngle={isMobile ? 0 : Math.PI / 6}
          enableDamping={!isMobile}
          dampingFactor={0.05}
        />
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Navigation Arrows - Mobile optimized */}
        <button
          onClick={goToPrevious}
          className={`absolute left-2 md:left-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-md rounded-full ${
            isMobile ? 'p-2 opacity-70' : 'p-3 opacity-0 group-hover:opacity-100'
          } transition-all duration-300 hover:scale-110 pointer-events-auto border border-white/20`}
          aria-label="Previous image"
        >
          <ChevronLeft className={`${isMobile ? 'w-5 h-5' : 'w-6 h-6'} text-white`} />
        </button>

        <button
          onClick={goToNext}
          className={`absolute right-2 md:right-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-md rounded-full ${
            isMobile ? 'p-2 opacity-70' : 'p-3 opacity-0 group-hover:opacity-100'
          } transition-all duration-300 hover:scale-110 pointer-events-auto border border-white/20`}
          aria-label="Next image"
        >
          <ChevronRight className={`${isMobile ? 'w-5 h-5' : 'w-6 h-6'} text-white`} />
        </button>

        {/* Content Info - Mobile responsive */}
        <div className={`absolute ${isMobile ? 'bottom-4 left-4 right-4' : 'bottom-6 left-6 right-6'} text-white`}>
          <div className={`bg-black/20 backdrop-blur-md rounded-xl ${isMobile ? 'p-3' : 'p-4'} border border-white/10`}>
            <h3 className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold mb-2 ${
              isMobile ? 'opacity-100 translate-y-0' : 'transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100'
            } transition-all duration-500 delay-100`}>
              {images[currentIndex].title}
            </h3>
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} opacity-90 ${
              isMobile ? 'opacity-100 translate-y-0' : 'transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100'
            } transition-all duration-500 delay-200`}>
              {images[currentIndex].description}
            </p>
          </div>
        </div>

        {/* Dots Indicator - Mobile responsive */}
        <div className={`absolute ${isMobile ? 'bottom-2' : 'bottom-4'} left-1/2 -translate-x-1/2 flex ${
          isMobile ? 'space-x-2' : 'space-x-3'
        } pointer-events-auto`}>
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`${isMobile ? 'w-2 h-2' : 'w-3 h-3'} rounded-full transition-all duration-300 border border-white/30 ${
                index === currentIndex
                  ? `bg-white ${isMobile ? 'w-6' : 'w-8'} shadow-lg`
                  : 'bg-white/30 hover:bg-white/60'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {/* Auto-play Control - Mobile optimized */}
        <button
          onClick={toggleAutoPlay}
          className={`absolute ${isMobile ? 'top-2 right-2' : 'top-4 right-4'} bg-white/10 hover:bg-white/20 backdrop-blur-md rounded-full ${
            isMobile ? 'p-2 opacity-70' : 'p-3 opacity-0 group-hover:opacity-100'
          } transition-all duration-300 pointer-events-auto border border-white/20`}
          aria-label={isAutoPlaying ? 'Pause slideshow' : 'Play slideshow'}
        >
          {isAutoPlaying ? (
            <Pause className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-white`} />
          ) : (
            <Play className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-white`} />
          )}
        </button>
      </div>
    </div>
  )
}

export default ThreeDImageCarousel
