<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elite Structure Company - Professional Brochure</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .brochure-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .page {
            width: 100%;
            min-height: 297mm;
            padding: 20mm;
            page-break-after: always;
            position: relative;
            overflow: hidden;
        }

        .page:last-child {
            page-break-after: avoid;
        }

        /* Page 1 - Cover */
        .cover-page {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .three-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.3;
        }

        .construction-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('public/b1.jpg');
            background-size: cover;
            background-position: center;
            opacity: 0.2;
            z-index: 0;
        }

        .cover-page::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .logo-section {
            margin-bottom: 40px;
            z-index: 2;
        }

        .company-logo {
            width: 150px;
            height: 150px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 20px;
            overflow: hidden;
        }

        .company-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .main-title {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            z-index: 2;
        }

        .subtitle {
            font-size: 1.5em;
            margin-bottom: 30px;
            opacity: 0.9;
            z-index: 2;
        }

        .tagline {
            font-size: 1.2em;
            font-style: italic;
            margin-bottom: 40px;
            z-index: 2;
        }

        .certifications-banner {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            z-index: 2;
        }

        .cert-item {
            display: inline-block;
            margin: 0 15px;
            font-weight: bold;
        }

        /* Page 2 - About Us */
        .about-page {
            background: white;
            position: relative;
        }

        .about-bg-image {
            position: absolute;
            top: 0;
            right: 0;
            width: 40%;
            height: 100%;
            background-image: url('public/b2.jpg');
            background-size: cover;
            background-position: center;
            opacity: 0.1;
            z-index: 0;
        }

        .about-content {
            position: relative;
            z-index: 2;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #3b82f6;
        }

        .page-title {
            font-size: 2.5em;
            color: #1e3a8a;
            margin-bottom: 10px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .content-box {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #3b82f6;
        }

        .content-box h3 {
            color: #1e3a8a;
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-top: 30px;
        }

        .stat-box {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #3b82f6, #1e3a8a);
            color: white;
            border-radius: 15px;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* Page 3 - Services */
        .services-page {
            background: white;
            position: relative;
        }

        .services-bg-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('public/b3.jpg');
            background-size: cover;
            background-position: center;
            opacity: 0.05;
            z-index: 0;
        }

        .services-content {
            position: relative;
            z-index: 2;
        }

        .services-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }

        .service-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e5e7eb 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e5e7eb;
            transition: transform 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px) rotateY(5deg);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .service-card {
            transform-style: preserve-3d;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #1e3a8a);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: white;
            font-size: 1.5em;
        }

        .service-title {
            color: #1e3a8a;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .service-features {
            list-style: none;
            margin-top: 15px;
        }

        .service-features li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .service-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #3b82f6;
            font-weight: bold;
        }

        /* Page 4 - Contact */
        .contact-page {
            background: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .contact-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
        }

        .contact-section h3 {
            color: #1e3a8a;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .contact-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }

        .contact-label {
            font-weight: bold;
            color: #1e3a8a;
        }

        .contact-value {
            color: #666;
            margin-top: 5px;
        }

        .team-member {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 1px solid #e5e7eb;
        }

        .member-name {
            font-weight: bold;
            color: #1e3a8a;
        }

        .member-position {
            color: #3b82f6;
            font-size: 0.9em;
        }

        .member-phone {
            color: #666;
            font-size: 0.9em;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-icon {
            position: absolute;
            font-size: 2em;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 15%; right: 10%; animation-delay: 3s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        .parallax-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 120%;
            height: 120%;
            background-size: cover;
            background-position: center;
            will-change: transform;
        }

        @media print {
            body { background: white; }
            .brochure-container { box-shadow: none; }
            .page { page-break-after: always; }
            .three-canvas, .floating-elements { display: none; }
        }
    </style>
</head>
<body>
    <div class="brochure-container">
        <!-- Page 1: Cover -->
        <div class="page cover-page">
            <div class="construction-bg"></div>
            <canvas class="three-canvas" id="threeCanvas"></canvas>
            <div class="floating-elements">
                <div class="floating-icon">🏗️</div>
                <div class="floating-icon">⚙️</div>
                <div class="floating-icon">⚡</div>
                <div class="floating-icon">🔧</div>
            </div>
            <div class="logo-section">
                <div class="company-logo">
                    <img src="src/assets/elite-removebg-preview.png" alt="Elite Structure Company Logo" />
                </div>
            </div>
            
            <h1 class="main-title">ELITE STRUCTURE</h1>
            <h2 class="subtitle">COMPANY</h2>
            <p class="tagline">Premier Construction & Engineering Services</p>
            
            <div class="certifications-banner">
                <div class="cert-item">🏆 ISO 9001 CERTIFIED</div>
                <div class="cert-item">⭐ SAUDI ARAMCO APPROVED</div>
                <div class="cert-item">✅ SABIC CERTIFIED</div>
            </div>
            
            <div style="position: absolute; bottom: 30px; text-align: center; width: 100%;">
                <p style="font-size: 1.1em; opacity: 0.9;">Building Excellence Since 2008</p>
                <p style="font-size: 0.9em; opacity: 0.8;">Jubail • Taif • Bahrain</p>
            </div>
        </div>

        <!-- Page 2: About Us -->
        <div class="page about-page">
            <div class="about-bg-image"></div>
            <div class="about-content">
            <div class="page-header">
                <h1 class="page-title">About Elite Structure Company</h1>
                <p style="color: #666; font-size: 1.1em;">Building Excellence Since 2008</p>
            </div>

            <div class="content-grid">
                <div class="content-box">
                    <h3>Our Heritage</h3>
                    <p>Elite Structure Company (formerly MRB Contracting Co Ltd.) is an ISO 9001 Certified Company with proven expertise in the Saudi market since 2008. We are a premier sub-contractor for prestigious companies including SAUDI ARAMCO, SABIC, SAUDI ELECTRICITY COMPANY, and Maaden.</p>
                </div>

                <div class="content-box">
                    <h3>Our Mission</h3>
                    <p>To provide exceptional construction and engineering services that conform to specific client requirements and regulatory standards, ensuring quality, safety, and environmental protection in every project we undertake.</p>
                </div>
            </div>

            <div class="content-grid">
                <div class="content-box">
                    <h3>Quality Assurance</h3>
                    <ul style="list-style: none; padding-left: 0;">
                        <li style="padding: 5px 0; padding-left: 20px; position: relative;">
                            <span style="position: absolute; left: 0; color: #3b82f6; font-weight: bold;">✓</span>
                            ISO 9001 Quality Management Systems
                        </li>
                        <li style="padding: 5px 0; padding-left: 20px; position: relative;">
                            <span style="position: absolute; left: 0; color: #3b82f6; font-weight: bold;">✓</span>
                            SAUDI ARAMCO Approved Contractor
                        </li>
                        <li style="padding: 5px 0; padding-left: 20px; position: relative;">
                            <span style="position: absolute; left: 0; color: #3b82f6; font-weight: bold;">✓</span>
                            SABIC Certified Partner
                        </li>
                        <li style="padding: 5px 0; padding-left: 20px; position: relative;">
                            <span style="position: absolute; left: 0; color: #3b82f6; font-weight: bold;">✓</span>
                            Comprehensive Safety Programs
                        </li>
                    </ul>
                </div>

                <div class="content-box">
                    <h3>Strategic Locations</h3>
                    <div style="margin-bottom: 15px;">
                        <strong style="color: #1e3a8a;">Head Office:</strong><br>
                        Jubail, Saudi Arabia
                    </div>
                    <div style="margin-bottom: 15px;">
                        <strong style="color: #1e3a8a;">Branch Offices:</strong><br>
                        Taif, Saudi Arabia<br>
                        Bahrain
                    </div>
                </div>
            </div>

            <div class="stats-section">
                <div class="stat-box">
                    <span class="stat-number">15+</span>
                    <span class="stat-label">Years of Excellence</span>
                </div>
                <div class="stat-box">
                    <span class="stat-number">100+</span>
                    <span class="stat-label">Projects Completed</span>
                </div>
                <div class="stat-box">
                    <span class="stat-number">50+</span>
                    <span class="stat-label">Expert Team</span>
                </div>
                <div class="stat-box">
                    <span class="stat-number">3</span>
                    <span class="stat-label">Office Locations</span>
                </div>
            </div>

            <div style="margin-top: 40px; text-align: center; padding: 25px; background: linear-gradient(135deg, #1e3a8a, #3b82f6); color: white; border-radius: 15px;">
                <h3 style="margin-bottom: 15px;">Our Commitment</h3>
                <p style="font-size: 1.1em; opacity: 0.9;">We are committed to delivering exceptional results while maintaining the highest standards of safety, quality, and environmental responsibility.</p>
            </div>
            </div>
        </div>

        <!-- Page 3: Our Services -->
        <div class="page services-page">
            <div class="services-bg-image"></div>
            <div class="services-content">
            <div class="page-header">
                <h1 class="page-title">Our Services</h1>
                <p style="color: #666; font-size: 1.1em;">Comprehensive Construction & Engineering Solutions</p>
            </div>

            <div class="services-grid">
                <div class="service-card" style="background-image: linear-gradient(rgba(255,255,255,0.95), rgba(255,255,255,0.95)), url('public/b1.jpg'); background-size: cover; background-position: center;">
                    <div class="service-icon">🏗️</div>
                    <h3 class="service-title">Oil & Gas Construction</h3>
                    <p>Specialized in pipeline installation and maintenance, GRP/RTR piping systems, process equipment installation, and offshore/onshore facilities.</p>
                    <ul class="service-features">
                        <li>Pipeline Installation & Maintenance</li>
                        <li>GRP/RTR Piping Systems</li>
                        <li>Gas Processing Plants</li>
                        <li>Compression Stations</li>
                        <li>Offshore & Onshore Facilities</li>
                    </ul>
                </div>

                <div class="service-card" style="background-image: linear-gradient(rgba(255,255,255,0.95), rgba(255,255,255,0.95)), url('public/b2.jpg'); background-size: cover; background-position: center;">
                    <div class="service-icon">⚙️</div>
                    <h3 class="service-title">Refineries & Petrochemicals</h3>
                    <p>Expert mechanical construction, electrical & instrumentation works, shutdown/turnaround services, and process unit construction.</p>
                    <ul class="service-features">
                        <li>Mechanical Construction</li>
                        <li>Electrical & Instrumentation</li>
                        <li>Shutdown/Turnaround Services</li>
                        <li>Tank Farm Construction</li>
                        <li>Process Unit Installation</li>
                    </ul>
                </div>

                <div class="service-card" style="background-image: linear-gradient(rgba(255,255,255,0.95), rgba(255,255,255,0.95)), url('public/b3.jpg'); background-size: cover; background-position: center;">
                    <div class="service-icon">⚡</div>
                    <h3 class="service-title">Power & Desalination</h3>
                    <p>Civil construction works, steel structure erection, plant maintenance services, and boiler/turbine installation.</p>
                    <ul class="service-features">
                        <li>Civil Construction Works</li>
                        <li>Steel Structure Erection</li>
                        <li>Plant Maintenance Services</li>
                        <li>Water Treatment Systems</li>
                        <li>Boiler & Turbine Installation</li>
                    </ul>
                </div>

                <div class="service-card" style="background-image: linear-gradient(rgba(255,255,255,0.95), rgba(255,255,255,0.95)), url('public/b1.jpg'); background-size: cover; background-position: center;">
                    <div class="service-icon">🔧</div>
                    <h3 class="service-title">Specialized Services</h3>
                    <p>Non-metallic piping (GRP, RTR, HDPE), scaffolding services, industrial maintenance, and emergency repair services.</p>
                    <ul class="service-features">
                        <li>Non-metallic Piping (GRP, RTR, HDPE)</li>
                        <li>Scaffolding Services</li>
                        <li>Industrial Maintenance</li>
                        <li>Emergency Repair Services</li>
                        <li>Quality Control & Testing</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 40px; padding: 25px; background: #f8f9fa; border-radius: 15px; border-left: 5px solid #3b82f6;">
                <h3 style="color: #1e3a8a; margin-bottom: 15px;">Why Choose Elite Structure Company?</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <p><strong>✓ Proven Track Record:</strong> 15+ years of successful project delivery</p>
                        <p><strong>✓ Expert Team:</strong> Qualified engineers, technicians, and skilled workforce</p>
                        <p><strong>✓ Quality Assurance:</strong> ISO 9001 certified processes</p>
                    </div>
                    <div>
                        <p><strong>✓ Safety First:</strong> Comprehensive safety programs and protocols</p>
                        <p><strong>✓ Trusted Partner:</strong> Approved by SAUDI ARAMCO, SABIC, and more</p>
                        <p><strong>✓ Regional Presence:</strong> Strategic locations across Saudi Arabia and Bahrain</p>
                    </div>
                </div>
            </div>
            </div>
        </div>

        <!-- Page 4: Contact Information -->
        <div class="page contact-page">
            <div class="page-header">
                <h1 class="page-title">Contact Us</h1>
                <p style="color: #666; font-size: 1.1em;">Get in Touch with Our Expert Team</p>
            </div>

            <div class="contact-grid">
                <div class="contact-section">
                    <h3>📞 Key Contacts</h3>
                    <div class="team-member">
                        <div class="member-name">Nahar Rashid Buainain</div>
                        <div class="member-position">Chief Executive Officer (CEO)</div>
                        <div class="member-phone">📱 +966559789339</div>
                        <div style="color: #888; font-size: 0.8em; margin-top: 5px;">نهار راشد بوعيدين</div>
                    </div>

                    <div class="team-member">
                        <div class="member-name">G.M. Jilani</div>
                        <div class="member-position">General Manager</div>
                        <div class="member-phone">📱 +966502848208</div>
                        <div style="color: #888; font-size: 0.8em; margin-top: 5px;">جي إم جياشي</div>
                    </div>

                    <div class="team-member">
                        <div class="member-name">Ahmed Shaik.M</div>
                        <div class="member-position">Technical Manager</div>
                        <div class="member-phone">📱 +966539084883</div>
                        <div style="color: #888; font-size: 0.8em; margin-top: 5px;">أحمد شيخ م</div>
                    </div>

                    <div class="team-member">
                        <div class="member-name">Abdul Jaleel</div>
                        <div class="member-position">Administration</div>
                        <div class="member-phone">📱 +966571384069</div>
                        <div style="color: #888; font-size: 0.8em; margin-top: 5px;">عبد الحليل</div>
                    </div>
                </div>

                <div class="contact-section">
                    <h3>📧 Email & Locations</h3>
                    <div class="contact-item">
                        <div class="contact-label">General Inquiries</div>
                        <div class="contact-value"><EMAIL></div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-label">Projects</div>
                        <div class="contact-value"><EMAIL></div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-label">Careers</div>
                        <div class="contact-value"><EMAIL></div>
                    </div>

                    <h3 style="margin-top: 30px;">📍 Office Locations</h3>
                    <div class="contact-item">
                        <div class="contact-label">Head Office</div>
                        <div class="contact-value">Jubail, Saudi Arabia</div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-label">Branch Office</div>
                        <div class="contact-value">Taif, Saudi Arabia</div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-label">Branch Office</div>
                        <div class="contact-value">Bahrain</div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 40px; text-align: center; padding: 30px; background: linear-gradient(135deg, #1e3a8a, #3b82f6); color: white; border-radius: 15px;">
                <h2 style="margin-bottom: 20px;">Ready to Start Your Project?</h2>
                <p style="font-size: 1.2em; margin-bottom: 20px; opacity: 0.9;">
                    Contact us today to discuss your construction and engineering needs
                </p>
                <div style="display: flex; justify-content: center; gap: 30px; flex-wrap: wrap;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.1em; font-weight: bold;">📞 Call Us</div>
                        <div style="opacity: 0.9;">+966559789339</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.1em; font-weight: bold;">📧 Email Us</div>
                        <div style="opacity: 0.9;"><EMAIL></div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.1em; font-weight: bold;">📍 Visit Us</div>
                        <div style="opacity: 0.9;">Jubail, Saudi Arabia</div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 30px; text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <p style="color: #666; font-size: 0.9em;">
                    © 2024 Elite Structure Company. All rights reserved.<br>
                    <strong>Building Excellence Since 2008</strong> | Premier Sub-Contractor for SAUDI ARAMCO, SABIC, SAUDI ELECTRICITY COMPANY, and MAADEN
                </p>
            </div>
        </div>
    </div>

    <script>
        // Three.js Animation for Cover Page
        function initThreeJS() {
            const canvas = document.getElementById('threeCanvas');
            if (!canvas) return;

            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, canvas.offsetWidth / canvas.offsetHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });

            renderer.setSize(canvas.offsetWidth, canvas.offsetHeight);
            renderer.setClearColor(0x000000, 0);

            // Create floating geometric shapes
            const geometries = [
                new THREE.BoxGeometry(1, 1, 1),
                new THREE.ConeGeometry(0.5, 1, 8),
                new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
                new THREE.OctahedronGeometry(0.7)
            ];

            const materials = [
                new THREE.MeshBasicMaterial({ color: 0x3b82f6, wireframe: true, transparent: true, opacity: 0.6 }),
                new THREE.MeshBasicMaterial({ color: 0x60a5fa, wireframe: true, transparent: true, opacity: 0.4 }),
                new THREE.MeshBasicMaterial({ color: 0x1e3a8a, wireframe: true, transparent: true, opacity: 0.5 }),
                new THREE.MeshBasicMaterial({ color: 0x93c5fd, wireframe: true, transparent: true, opacity: 0.3 })
            ];

            const meshes = [];
            for (let i = 0; i < 15; i++) {
                const geometry = geometries[Math.floor(Math.random() * geometries.length)];
                const material = materials[Math.floor(Math.random() * materials.length)];
                const mesh = new THREE.Mesh(geometry, material);

                mesh.position.x = (Math.random() - 0.5) * 20;
                mesh.position.y = (Math.random() - 0.5) * 20;
                mesh.position.z = (Math.random() - 0.5) * 20;

                mesh.rotation.x = Math.random() * Math.PI;
                mesh.rotation.y = Math.random() * Math.PI;

                scene.add(mesh);
                meshes.push(mesh);
            }

            camera.position.z = 15;

            // Animation loop
            function animate() {
                requestAnimationFrame(animate);

                meshes.forEach((mesh, index) => {
                    mesh.rotation.x += 0.005 + index * 0.001;
                    mesh.rotation.y += 0.005 + index * 0.001;
                    mesh.position.y += Math.sin(Date.now() * 0.001 + index) * 0.01;
                });

                renderer.render(scene, camera);
            }

            animate();

            // Handle resize
            window.addEventListener('resize', () => {
                if (canvas.offsetWidth && canvas.offsetHeight) {
                    camera.aspect = canvas.offsetWidth / canvas.offsetHeight;
                    camera.updateProjectionMatrix();
                    renderer.setSize(canvas.offsetWidth, canvas.offsetHeight);
                }
            });
        }

        // GSAP Animations
        function initAnimations() {
            // Animate service cards on scroll
            gsap.registerPlugin(ScrollTrigger);

            gsap.from('.service-card', {
                duration: 0.8,
                y: 50,
                opacity: 0,
                stagger: 0.2,
                ease: 'power2.out',
                scrollTrigger: {
                    trigger: '.services-grid',
                    start: 'top 80%'
                }
            });

            // Animate stats
            gsap.from('.stat-box', {
                duration: 1,
                scale: 0.8,
                opacity: 0,
                stagger: 0.1,
                ease: 'back.out(1.7)',
                scrollTrigger: {
                    trigger: '.stats-section',
                    start: 'top 80%'
                }
            });

            // Animate team members
            gsap.from('.team-member', {
                duration: 0.6,
                x: -30,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out',
                scrollTrigger: {
                    trigger: '.contact-section',
                    start: 'top 80%'
                }
            });

            // Parallax effect for background images
            gsap.to('.about-bg-image', {
                yPercent: -50,
                ease: 'none',
                scrollTrigger: {
                    trigger: '.about-page',
                    start: 'top bottom',
                    end: 'bottom top',
                    scrub: true
                }
            });

            gsap.to('.services-bg-image', {
                yPercent: -30,
                ease: 'none',
                scrollTrigger: {
                    trigger: '.services-page',
                    start: 'top bottom',
                    end: 'bottom top',
                    scrub: true
                }
            });
        }

        // Enhanced hover effects
        function initInteractivity() {
            // Service card 3D tilt effect
            document.querySelectorAll('.service-card').forEach(card => {
                card.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;

                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;

                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
                });
            });

            // Smooth scroll for better experience
            document.documentElement.style.scrollBehavior = 'smooth';
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initThreeJS();

            // Load GSAP ScrollTrigger if available
            if (typeof ScrollTrigger !== 'undefined') {
                initAnimations();
            }

            initInteractivity();
        });

        // Print optimization
        window.addEventListener('beforeprint', () => {
            document.querySelectorAll('.three-canvas, .floating-elements').forEach(el => {
                el.style.display = 'none';
            });
        });

        window.addEventListener('afterprint', () => {
            document.querySelectorAll('.three-canvas, .floating-elements').forEach(el => {
                el.style.display = 'block';
            });
        });
    </script>

    <!-- GSAP ScrollTrigger Plugin -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</body>
</html>
