import { ArrowRight, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import ThreeDImageCarousel from './ThreeDImageCarousel'

const Hero = () => {
  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white overflow-hidden"
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        {/* Animated gradient overlay */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `
              linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%),
              linear-gradient(-45deg, transparent 30%, rgba(255,255,255,0.05) 50%, transparent 70%)
            `,
            animation: 'shimmer 8s ease-in-out infinite'
          }}
        />

        {/* Floating geometric shapes */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute opacity-10"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${20 + Math.random() * 40}px`,
                height: `${20 + Math.random() * 40}px`,
                background: 'linear-gradient(45deg, #60a5fa, #3b82f6)',
                borderRadius: Math.random() > 0.5 ? '50%' : '10%',
                animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 2}s`,
                transform: `rotate(${Math.random() * 360}deg)`
              }}
            />
          ))}
        </div>

        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 md:pt-20">
        <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-center">
          {/* Content */}
          <div
            className="text-center lg:text-left"
            style={{
              transform: 'perspective(1000px) rotateY(-5deg)',
              transformStyle: 'preserve-3d'
            }}
          >
            <div className="inline-flex items-center px-4 py-2 bg-accent/20 rounded-full text-sm font-medium text-accent-foreground mb-6">
              <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
              ISO 9001 Certified Company
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-4 md:mb-6">
              Premier Construction &{' '}
              <span className="text-primary">Engineering</span>{' '}
              Services
            </h1>

            <p className="text-lg md:text-xl text-gray-600 mb-6 md:mb-8 max-w-2xl">
              Elite Structure Company has been delivering excellence in the Saudi market since 2008.
              We are a premier sub-contractor for SAUDI ARAMCO, SABIC, and other prestigious companies.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 mb-6 md:mb-8">
              <Button
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-2xl transform hover:scale-105 transition-all duration-300 border-0 text-sm md:text-base"
                style={{
                  boxShadow: '0 10px 30px rgba(59, 130, 246, 0.4)'
                }}
              >
                Our Services
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-primary text-primary hover:bg-primary hover:text-white transform hover:scale-105 transition-all duration-300 text-sm md:text-base"
              >
                View Projects
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 md:gap-8 pt-6 md:pt-8 border-t border-gray-200">
              <div className="text-center lg:text-left">
                <div className="text-2xl md:text-3xl font-bold text-primary">15+</div>
                <div className="text-xs md:text-sm text-gray-600">Years Experience</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl md:text-3xl font-bold text-primary">500+</div>
                <div className="text-xs md:text-sm text-gray-600">Projects Completed</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl md:text-3xl font-bold text-primary">100%</div>
                <div className="text-xs md:text-sm text-gray-600">Client Satisfaction</div>
              </div>
            </div>
          </div>

          {/* 3D Image Carousel */}
          <div
            className="relative mt-8 lg:mt-0"
            style={{
              transform: window.innerWidth >= 1024 ? 'perspective(1000px) rotateY(5deg) rotateX(-2deg)' : 'none',
              transformStyle: 'preserve-3d'
            }}
          >
            <div
              className="aspect-square rounded-2xl overflow-hidden"
              style={{
                filter: 'drop-shadow(0 25px 50px rgba(0,0,0,0.4))',
                transform: window.innerWidth >= 1024 ? 'translateZ(50px)' : 'none'
              }}
            >
              <ThreeDImageCarousel />
            </div>

            {/* Floating elements around carousel - Hidden on mobile */}
            <div className="hidden lg:block absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-80 animate-pulse" />
            <div className="hidden lg:block absolute -bottom-6 -left-6 w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 animate-bounce" />
            <div className="hidden lg:block absolute top-1/2 -right-8 w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full opacity-70" style={{ animation: 'float 3s ease-in-out infinite' }} />
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}

export default Hero

